import request from '@/utils/request'

// 查询救援信息列表
export function listRescue(query) {
  return request({
    url: '/system/rescue/list',
    method: 'get',
    params: query
  })
}

// 查询救援信息详细
export function getRescue(id) {
  return request({
    url: '/system/rescue/' + id,
    method: 'get'
  })
}

// 新增救援信息
export function addRescue(data) {
  return request({
    url: '/system/rescue',
    method: 'post',
    data: data
  })
}

// 修改救援信息
export function updateRescue(data) {
  return request({
    url: '/system/rescue',
    method: 'put',
    data: data
  })
}

// 删除救援信息
export function delRescue(id) {
  return request({
    url: '/system/rescue/' + id,
    method: 'delete'
  })
}

// 分配救援人员
export function fpRescue(query) {
  return request({
    url: '/system/rescue/fp',
    method: 'get',
    params: query
  })
}
