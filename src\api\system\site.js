import request from '@/utils/request'

// 查询服务站点列表
export function listSite(query) {
  return request({
    url: '/system/site/list',
    method: 'get',
    params: query
  })
}

// 查询服务站点详细
export function getSite(id) {
  return request({
    url: '/system/site/' + id,
    method: 'get'
  })
}

// 新增服务站点
export function addSite(data) {
  return request({
    url: '/system/site',
    method: 'post',
    data: data
  })
}

// 修改服务站点
export function updateSite(data) {
  return request({
    url: '/system/site',
    method: 'put',
    data: data
  })
}

// 删除服务站点
export function delSite(id) {
  return request({
    url: '/system/site/' + id,
    method: 'delete'
  })
}
