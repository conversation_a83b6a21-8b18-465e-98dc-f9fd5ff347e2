<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="几群" prop="group">
        <el-input
          v-model="queryParams.group"
          placeholder="请输入几群"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="救援人员" prop="rescueUserName">
        <el-input
          v-model="queryParams.rescueUser"
          placeholder="请输入救援人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:rescue:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:rescue:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:rescue:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:rescue:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="rescueList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="id" align="center" prop="id"/>
      <el-table-column label="车型" align="center" prop="carType"/>
      <el-table-column label="车牌号" align="center" prop="carNo"/>
      <el-table-column label="手机号" align="center" prop="phone"/>
      <el-table-column label="地址" align="center" prop="address"/>
      <el-table-column label="详细地址" align="center" prop="detailAdd"/>
      <el-table-column label="几群" align="center" prop="group"/>
      <el-table-column label="救援人员" align="center" prop="rescueUserName"/>
      <el-table-column label="状态" align="center" prop="statusStr"/>
      <el-table-column label="救援事项" align="center" prop="desc"/>
      <el-table-column label="是否有标" align="center" prop="isTagStr"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:rescue:edit']"
          >分配
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:rescue:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:rescue:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog title="分配救援人员" :visible.sync="rescueDialogStatus" width="500px" append-to-body @close="cancelAllocation">
      <el-select v-model="rescuePerson" placeholder="请选择">
        <el-option
          v-for="item in rescuePersonList"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="allocationPerson">确 定</el-button>
        <el-button @click="cancelAllocation">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改救援信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号"/>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="车型" prop="carType">
          <el-input v-model="form.carType" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="车牌号" prop="carNo">
          <el-input v-model="form.carNo" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="详细地址" prop="detailAdd">
          <el-input v-model="form.detailAdd" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="几群" prop="group">
          <el-input v-model="form.group" placeholder="请输入几群"/>
        </el-form-item>
        <el-form-item label="救援事项" prop="desc">
          <el-input v-model="form.desc" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="是否有标[0无 1有]" prop="isTag">
          <el-input v-model="form.isTag" placeholder="请输入是否有标[0无 1有]"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listRescue, getRescue, delRescue, addRescue, updateRescue, fpRescue} from "@/api/system/rescue"
import { listJy } from "@/api/system/jy"

export default {
  name: "Rescue",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 救援信息表格数据
      rescueList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        carType: null,
        phone: null,
        address: null,
        detailAdd: null,
        group: null,
        rescueUser: null,
        rescueUserName: null,
        status: null,
        desc: null,
        isTag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},

      rescuePerson: '',
      rescueId: '',
      rescuePersonList: [],
      rescueDialogStatus: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询救援信息列表 */
    getList() {
      this.loading = true
      listRescue(this.queryParams).then(response => {
        this.rescueList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    cancelAllocation(){
      this.rescuePerson = ''
      this.rescueDialogStatus = false
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        carType: null,
        phone: null,
        carNo:null,
        address: null,
        detailAdd: null,
        group: null,
        rescueUser: null,
        status: null,
        desc: null,
        isTag: null,
        createTime: null,
        updateTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加救援信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getRescue(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改救援信息"
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.rescueId = row.id

      let data = {
        pageNum: 1,
        pageSize: 999
      }
      listJy(data).then(response => {
        this.rescueDialogStatus = true
        this.rescuePersonList = response.rows
      })
    },
    allocationPerson(){
      if(!this.rescuePerson){
        this.$modal.msgError("请先选择救援人员")
        return
      }
      let data ={
        id: this.rescueId,
        jyId: this.rescuePerson
      }
      fpRescue(data).then(res => {
        this.cancelAllocation()
        this.getList()
        this.$modal.msgSuccess("分配成功")
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRescue(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addRescue(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除救援信息编号为"' + ids + '"的数据项？').then(function () {
        return delRescue(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/rescue/export', {
        ...this.queryParams
      }, `rescue_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
