import request from '@/utils/request'

// 查询首页资讯列表
export function listZx(query) {
  return request({
    url: '/system/zx/list',
    method: 'get',
    params: query
  })
}

// 查询首页资讯详细
export function getZx(id) {
  return request({
    url: '/system/zx/' + id,
    method: 'get'
  })
}

// 新增首页资讯
export function addZx(data) {
  return request({
    url: '/system/zx',
    method: 'post',
    data: data
  })
}

// 修改首页资讯
export function updateZx(data) {
  return request({
    url: '/system/zx',
    method: 'put',
    data: data
  })
}

// 删除首页资讯
export function delZx(id) {
  return request({
    url: '/system/zx/' + id,
    method: 'delete'
  })
}
