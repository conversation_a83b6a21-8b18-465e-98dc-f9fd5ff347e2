<template>
  <div class="app-container home">
    <!-- 第一个图表：今日活跃用户数 - 渐变柱状图 -->
    <div class="chart-container">
      <div class="chart-title">今日活跃用户数</div>
      <div id="activeUsersChart" class="chart"></div>
    </div>

    <!-- 第二行：两个图表并排 -->
    <div class="chart-row">
      <!-- 第二个图表：今日发送救援人数 - 折线图 -->
      <div class="chart-container half-width">
        <div class="chart-title">今日发送救援人数</div>
        <div id="rescueChart" class="chart"></div>
      </div>

      <!-- 第三个图表：不同城市员工数 - 饼图 -->
      <div class="chart-container half-width">
        <div class="chart-title">不同城市员工数</div>
        <div id="employeeChart" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: "Index",
  data() {
    return {
      activeUsersChart: null,
      rescueChart: null,
      employeeChart: null
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.activeUsersChart) {
      this.activeUsersChart.dispose()
    }
    if (this.rescueChart) {
      this.rescueChart.dispose()
    }
    if (this.employeeChart) {
      this.employeeChart.dispose()
    }
  },
  methods: {
    initCharts() {
      this.initActiveUsersChart()
      this.initRescueChart()
      this.initEmployeeChart()
    },

    // 初始化今日活跃用户数图表（渐变柱状图）
    initActiveUsersChart() {
      this.activeUsersChart = echarts.init(document.getElementById('activeUsersChart'))

      // 模拟数据
      const hours = ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00']
      const activeUsers = [120, 80, 60, 90, 180, 320, 450, 380, 420, 350, 280, 200]

      const option = {
        title: {
          text: '活跃用户趋势',
          left: 'center',
          textStyle: {
            color: '#333',
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}<br/>活跃用户: {c}人'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: hours,
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [{
          data: activeUsers,
          type: 'bar',
          barWidth: '30%', // 设置柱状图宽度为30%，使其更细
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2378f7' },
                { offset: 0.7, color: '#2378f7' },
                { offset: 1, color: '#83bff6' }
              ])
            }
          }
        }]
      }

      this.activeUsersChart.setOption(option)
    },

    // 初始化今日发送救援人数图表（折线图）
    initRescueChart() {
      this.rescueChart = echarts.init(document.getElementById('rescueChart'))

      // 模拟数据
      const times = ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00']
      const rescueCount = [5, 12, 8, 15, 20, 18, 10, 6]

      const option = {
        title: {
          text: '救援人数趋势',
          left: 'center',
          textStyle: {
            color: '#333',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>救援人数: {c}人'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: times,
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [{
          data: rescueCount,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#ff6b6b',
            width: 3
          },
          itemStyle: {
            color: '#ff6b6b',
            borderColor: '#fff',
            borderWidth: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 107, 107, 0.3)' },
              { offset: 1, color: 'rgba(255, 107, 107, 0.1)' }
            ])
          }
        }]
      }

      this.rescueChart.setOption(option)
    },

    // 初始化不同城市员工数图表（饼图）
    initEmployeeChart() {
      this.employeeChart = echarts.init(document.getElementById('employeeChart'))

      // 模拟数据
      const cityData = [
        { value: 335, name: '北京' },
        { value: 310, name: '上海' },
        { value: 234, name: '广州' },
        { value: 135, name: '深圳' },
        { value: 148, name: '杭州' },
        { value: 98, name: '成都' }
      ]

      const option = {
        title: {
          text: '城市员工分布',
          left: 'center',
          textStyle: {
            color: '#333',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a}<br/>{b}: {c}人 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '员工数',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {c}人\n({d}%)',
            fontSize: 12,
            color: '#333'
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10,
            lineStyle: {
              color: '#999'
            }
          },
          data: cityData,
          color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']
        }]
      }

      this.employeeChart.setOption(option)
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
}

.chart-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 20px;

  .chart-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
  }

  .chart {
    width: 100%;
    height: 400px;
  }
}

.chart-row {
  display: flex;
  gap: 20px;

  .half-width {
    flex: 1;
    margin-bottom: 0;

    .chart {
      height: 350px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chart-row {
    flex-direction: column;

    .half-width {
      margin-bottom: 20px;
    }
  }
}
</style>

