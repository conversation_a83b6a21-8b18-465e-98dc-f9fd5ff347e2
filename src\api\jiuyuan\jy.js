import request from '@/utils/request'

// 查询救援人员列表
export function listJy(query) {
  return request({
    url: '/jiuyuan/jy/list',
    method: 'get',
    params: query
  })
}

// 查询救援人员详细
export function getJy(id) {
  return request({
    url: '/jiuyuan/jy/' + id,
    method: 'get'
  })
}

// 新增救援人员
export function addJy(data) {
  return request({
    url: '/jiuyuan/jy',
    method: 'post',
    data: data
  })
}

// 修改救援人员
export function updateJy(data) {
  return request({
    url: '/jiuyuan/jy',
    method: 'put',
    data: data
  })
}

// 删除救援人员
export function delJy(id) {
  return request({
    url: '/jiuyuan/jy/' + id,
    method: 'delete'
  })
}
